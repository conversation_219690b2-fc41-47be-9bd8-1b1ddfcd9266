# backlogger2 Development Guidelines

Auto-generated from all feature plans. Last updated: 2025-09-07

## Active Technologies
- Python 3.11+ with uv package manager (001-backlogger-is-a)
- FastAPI + SQLAlchemy + SQLite (001-backlogger-is-a)
- Dags<PERSON> for task management (001-backlogger-is-a)
- Jinja<PERSON> for dashboard templating (001-backlogger-is-a)
- WebSocket for real-time updates (001-backlogger-is-a)

## Project Structure
```
src/backlogger/
├── models/          # SQLAlchemy entities
├── api/             # FastAPI endpoints
├── dashboard/       # Jinja2 templates
├── ai_workflows/    # Dagster tasks
├── core/            # Business logic
└── cli/             # Command interface
tests/
├── contract/        # API contract tests
├── integration/     # Cross-component tests
└── unit/            # Library unit tests
```

## Commands
# Use uv for all Python operations
uv init                 # Initialize project
uv add package-name     # Add dependencies
uv run script-name.py   # Run applications
uv run pytest          # Run tests

## Code Style
Python: Follow FastAPI conventions, use type hints, async/await patterns
Testing: TDD with pytest, contract-first development
Database: SQLAlchemy models with proper relationships and validation

## Recent Changes
- 001-backlogger-is-a: Added AI-integrated backlog management system with hierarchical work organization (Projects→Epics→Features→BacklogItems→Tasks) and sequential AI task processing via Dagster workflows

<!-- MANUAL ADDITIONS START -->
When trying to use any library, ALWAYS refer to the tool context7 to get the documentation of the part you are trying to implement. ALWAYS DO THIS.
<!-- MANUAL ADDITIONS END -->