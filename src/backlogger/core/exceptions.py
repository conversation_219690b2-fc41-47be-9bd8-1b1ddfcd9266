"""Custom exceptions for the Backlogger application."""


class BackloggerError(Exception):
    """Base exception class for all Backlogger errors."""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(message)


class NotFoundError(BackloggerError):
    """Raised when a requested resource is not found."""
    pass


class DuplicateNameError(BackloggerError):
    """Raised when attempting to create a resource with a duplicate name."""
    pass


class ValidationError(BackloggerError):
    """Raised when data validation fails."""
    pass


class InvalidStatusTransitionError(BackloggerError):
    """Raised when an invalid status transition is attempted."""
    pass


class InvalidOperationError(BackloggerError):
    """Raised when an invalid operation is attempted."""
    pass


class ConstraintViolationError(BackloggerError):
    """Raised when a database constraint is violated."""
    pass


class PermissionError(BackloggerError):
    """Raised when a user lacks permission for an operation."""
    pass


class ExternalServiceError(BackloggerError):
    """Raised when an external service (like Dagster) is unavailable."""
    pass


class QuotaExceededError(BackloggerError):
    """Raised when a quota or rate limit is exceeded."""
    pass


class ConcurrencyLimitError(BackloggerError):
    """Raised when concurrency limits are exceeded."""
    pass


class NoWorkflowError(BackloggerError):
    """Raised when no AI workflow is associated with a backlog item."""
    pass
