"""Epic and feature endpoints with validation."""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, Query, status
from pydantic import BaseModel, ConfigDict, Field, field_validator
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_async_db
from ..core.epic_service import EpicService
from ..core.feature_service import FeatureService
from ..models.epic import WorkItemStatus

# Pydantic models for Epics
class EpicCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Epic name")
    description: Optional[str] = Field(None, max_length=2000, description="Epic description")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty or whitespace only')
        return v.strip()


class EpicUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Epic name")
    description: Optional[str] = Field(None, max_length=2000, description="Epic description")
    status: Optional[WorkItemStatus] = Field(None, description="Epic status")
    assignee: Optional[str] = Field(None, max_length=100, description="Epic assignee")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Name cannot be empty or whitespace only')
        return v.strip() if v else v


class EpicResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    project_id: int
    created_at: datetime
    updated_at: datetime


class EpicListResponse(BaseModel):
    epics: List[EpicResponse]


# Pydantic models for Features
class FeatureCreate(BaseModel):
    name: str = Field(..., max_length=200, description="Feature name")
    description: Optional[str] = Field(None, max_length=2000, description="Feature description")


class FeatureUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200, description="Feature name")
    description: Optional[str] = Field(None, max_length=2000, description="Feature description")
    status: Optional[WorkItemStatus] = Field(None, description="Feature status")
    assignee: Optional[str] = Field(None, max_length=100, description="Feature assignee")


class FeatureResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    epic_id: int
    created_at: datetime
    updated_at: datetime


class FeatureListResponse(BaseModel):
    features: List[FeatureResponse]


# Router setup
router = APIRouter(prefix="/api/v1", tags=["work-items"])


# Epic endpoints
@router.get("/projects/{project_id}/epics", response_model=EpicListResponse)
async def list_epics(
    project_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by epic status"),
    assignee: Optional[str] = Query(None, description="Filter by assignee"),
    db: AsyncSession = Depends(get_async_db)
):
    """List epics within a project."""
    service = EpicService(db)

    epics = await service.list_epics_by_project(
        project_id=project_id,
        status_filter=status,
        assignee_filter=assignee
    )

    return EpicListResponse(
        epics=[EpicResponse.model_validate(epic) for epic in epics]
    )


@router.post("/projects/{project_id}/epics", response_model=EpicResponse, status_code=status.HTTP_201_CREATED)
async def create_epic(
    project_id: int,
    epic_data: EpicCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new epic."""
    service = EpicService(db)

    epic = await service.create_epic(
        project_id=project_id,
        name=epic_data.name,
        description=epic_data.description
    )

    return EpicResponse.model_validate(epic)


@router.put("/epics/{epic_id}", response_model=EpicResponse)
async def update_epic(
    epic_id: int,
    epic_data: EpicUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """Update epic status and assignee."""
    service = EpicService(db)

    epic = await service.update_epic(
        epic_id=epic_id,
        name=epic_data.name,
        description=epic_data.description,
        status=epic_data.status,
        assignee=epic_data.assignee
    )

    return EpicResponse.model_validate(epic)


@router.delete("/epics/{epic_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_epic(
    epic_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Delete epic and all child entities."""
    service = EpicService(db)
    await service.delete_epic(epic_id)
    return None


# Feature endpoints
@router.get("/epics/{epic_id}/features", response_model=FeatureListResponse)
async def list_features(
    epic_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by feature status"),
    assignee: Optional[str] = Query(None, description="Filter by assignee"),
    db: AsyncSession = Depends(get_async_db)
):
    """List features within an epic."""
    service = FeatureService(db)

    features = await service.list_features_by_epic(
        epic_id=epic_id,
        status_filter=status,
        assignee_filter=assignee
    )

    return FeatureListResponse(
        features=[FeatureResponse.model_validate(feature) for feature in features]
    )


@router.post("/epics/{epic_id}/features", response_model=FeatureResponse, status_code=status.HTTP_201_CREATED)
async def create_feature(
    epic_id: int,
    feature_data: FeatureCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new feature."""
    service = FeatureService(db)

    feature = await service.create_feature(
        epic_id=epic_id,
        name=feature_data.name,
        description=feature_data.description
    )

    return FeatureResponse.model_validate(feature)


@router.put("/features/{feature_id}", response_model=FeatureResponse)
async def update_feature(
    feature_id: int,
    feature_data: FeatureUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """Update feature status and assignee."""
    service = FeatureService(db)

    feature = await service.update_feature(
        feature_id=feature_id,
        name=feature_data.name,
        description=feature_data.description,
        status=feature_data.status,
        assignee=feature_data.assignee
    )

    return FeatureResponse.model_validate(feature)


@router.delete("/features/{feature_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_feature(
    feature_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Delete feature and all child entities."""
    service = FeatureService(db)
    await service.delete_feature(feature_id)
    return None