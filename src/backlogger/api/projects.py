"""FastAPI app setup and project endpoints."""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, Query, status
from pydantic import BaseModel, ConfigDict, Field, field_validator
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_async_db
from ..core.project_service import ProjectService
from ..models.project import ProjectStatus

# Pydantic models for request/response
class ProjectCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Project name")
    description: Optional[str] = Field(None, max_length=2000, description="Project description")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty or whitespace only')
        return v.strip()


class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Project name")
    description: Optional[str] = Field(None, max_length=2000, description="Project description")
    status: Optional[ProjectStatus] = Field(None, description="Project status")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Name cannot be empty or whitespace only')
        return v.strip() if v else v


class ProjectResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    status: ProjectStatus
    created_at: datetime
    updated_at: datetime


class ProjectListResponse(BaseModel):
    projects: List[ProjectResponse]
    total: int
    limit: int
    offset: int


class ProjectDetailResponse(ProjectResponse):
    epics: Optional[List[dict]] = None  # Will be populated if include=epics


# Router setup
router = APIRouter(prefix="/api/v1", tags=["projects"])


@router.get("/projects", response_model=ProjectListResponse)
async def list_projects(
    status: Optional[ProjectStatus] = Query(None, description="Filter by project status"),
    limit: int = Query(50, ge=1, le=100, description="Number of projects to return"),
    offset: int = Query(0, ge=0, description="Number of projects to skip"),
    db: AsyncSession = Depends(get_async_db)
):
    """List all projects with optional filtering."""
    service = ProjectService(db)

    projects = await service.list_projects(
        status_filter=status,
        limit=limit,
        offset=offset
    )

    total = await service.count_projects(status_filter=status)

    return ProjectListResponse(
        projects=[ProjectResponse.model_validate(project) for project in projects],
        total=total,
        limit=limit,
        offset=offset
    )


@router.post("/projects", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new project."""
    service = ProjectService(db)

    project = await service.create_project(
        name=project_data.name,
        description=project_data.description
    )

    return ProjectResponse.model_validate(project)


@router.get("/projects/{project_id}", response_model=ProjectDetailResponse)
async def get_project(
    project_id: int,
    include: Optional[str] = Query(None, description="Include related data (epics, epics.features, etc.)"),
    db: AsyncSession = Depends(get_async_db)
):
    """Get project details with optional related data."""
    service = ProjectService(db)

    include_epics = include and "epics" in include
    project = await service.get_project(project_id, include_epics=include_epics)

    response_data = ProjectDetailResponse.model_validate(project)

    # Add epics data if requested
    if include_epics and project.epics:
        response_data.epics = [
            {
                "id": epic.id,
                "name": epic.name,
                "status": epic.status,
                "assignee": epic.assignee
            }
            for epic in project.epics
        ]

    return response_data


@router.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """Update project information."""
    service = ProjectService(db)

    project = await service.update_project(
        project_id=project_id,
        name=project_data.name,
        description=project_data.description,
        status=project_data.status
    )

    return ProjectResponse.model_validate(project)


@router.delete("/projects/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Delete project and all child entities."""
    service = ProjectService(db)
    await service.delete_project(project_id)
    return None