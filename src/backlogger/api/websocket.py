"""WebSocket handler for real-time updates."""

import json
import logging
from datetime import datetime
from typing import Dict, List, Set

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class WebSocketMessage(BaseModel):
    """Base WebSocket message structure."""
    action: str
    resource: str
    resource_id: int
    data: dict = {}
    timestamp: str = ""


class SubscriptionMessage(BaseModel):
    """Client subscription message."""
    action: str = "subscribe"
    resource: str
    resource_id: int


class UpdateMessage(BaseModel):
    """Server update message."""
    event: str
    resource: str
    resource_id: int
    data: dict
    timestamp: str


class ConnectionManager:
    """Manages WebSocket connections and message broadcasting."""

    def __init__(self):
        # Active connections: {websocket: {resource_type: {resource_id}}}
        self.active_connections: Dict[WebSocket, Dict[str, Set[int]]] = {}

    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections[websocket] = {}
        logger.info(f"WebSocket connected: {websocket.client}")

    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            del self.active_connections[websocket]
            logger.info(f"WebSocket disconnected: {websocket.client}")

    async def subscribe(self, websocket: WebSocket, resource: str, resource_id: int):
        """Subscribe a connection to updates for a specific resource."""
        if websocket not in self.active_connections:
            return

        if resource not in self.active_connections[websocket]:
            self.active_connections[websocket][resource] = set()

        self.active_connections[websocket][resource].add(resource_id)
        logger.info(f"WebSocket {websocket.client} subscribed to {resource}:{resource_id}")

    async def unsubscribe(self, websocket: WebSocket, resource: str, resource_id: int):
        """Unsubscribe a connection from updates for a specific resource."""
        if websocket not in self.active_connections:
            return

        if resource in self.active_connections[websocket]:
            self.active_connections[websocket][resource].discard(resource_id)
            if not self.active_connections[websocket][resource]:
                del self.active_connections[websocket][resource]

        logger.info(f"WebSocket {websocket.client} unsubscribed from {resource}:{resource_id}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Failed to send message to {websocket.client}: {e}")
            self.disconnect(websocket)

    async def broadcast_update(self, event: str, resource: str, resource_id: int, data: dict):
        """Broadcast an update to all subscribed connections."""
        message = UpdateMessage(
            event=event,
            resource=resource,
            resource_id=resource_id,
            data=data,
            timestamp=datetime.utcnow().isoformat()
        )

        message_text = message.model_dump_json()

        # Find all connections subscribed to this resource
        disconnected_connections = []

        for websocket, subscriptions in self.active_connections.items():
            if resource in subscriptions and resource_id in subscriptions[resource]:
                try:
                    await websocket.send_text(message_text)
                except Exception as e:
                    logger.error(f"Failed to broadcast to {websocket.client}: {e}")
                    disconnected_connections.append(websocket)

        # Clean up disconnected connections
        for websocket in disconnected_connections:
            self.disconnect(websocket)

        logger.info(f"Broadcasted {event} for {resource}:{resource_id} to {len(self.active_connections) - len(disconnected_connections)} connections")


# Global connection manager instance
manager = ConnectionManager()


# Router setup
router = APIRouter(tags=["websocket"])


@router.websocket("/ws/dashboard")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time dashboard updates."""
    await manager.connect(websocket)

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()

            try:
                message_data = json.loads(data)

                # Handle subscription requests
                if message_data.get("action") == "subscribe":
                    resource = message_data.get("resource")
                    resource_id = message_data.get("resource_id")

                    if resource and resource_id is not None:
                        await manager.subscribe(websocket, resource, resource_id)

                        # Send confirmation
                        response = {
                            "action": "subscribed",
                            "resource": resource,
                            "resource_id": resource_id,
                            "timestamp": datetime.utcnow().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(response), websocket)

                # Handle unsubscription requests
                elif message_data.get("action") == "unsubscribe":
                    resource = message_data.get("resource")
                    resource_id = message_data.get("resource_id")

                    if resource and resource_id is not None:
                        await manager.unsubscribe(websocket, resource, resource_id)

                        # Send confirmation
                        response = {
                            "action": "unsubscribed",
                            "resource": resource,
                            "resource_id": resource_id,
                            "timestamp": datetime.utcnow().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(response), websocket)

                else:
                    # Unknown action
                    error_response = {
                        "error": "unknown_action",
                        "message": f"Unknown action: {message_data.get('action')}",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(error_response), websocket)

            except json.JSONDecodeError:
                error_response = {
                    "error": "invalid_json",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_response), websocket)

            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                error_response = {
                    "error": "processing_error",
                    "message": "Error processing message",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_response), websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)


# Utility functions for broadcasting updates from other parts of the application
async def broadcast_project_update(event: str, project_id: int, data: dict):
    """Broadcast project update to subscribed clients."""
    await manager.broadcast_update(event, "project", project_id, data)


async def broadcast_epic_update(event: str, epic_id: int, data: dict):
    """Broadcast epic update to subscribed clients."""
    await manager.broadcast_update(event, "epic", epic_id, data)


async def broadcast_feature_update(event: str, feature_id: int, data: dict):
    """Broadcast feature update to subscribed clients."""
    await manager.broadcast_update(event, "feature", feature_id, data)


async def broadcast_backlog_item_update(event: str, item_id: int, data: dict):
    """Broadcast backlog item update to subscribed clients."""
    await manager.broadcast_update(event, "backlog_item", item_id, data)


async def broadcast_task_update(event: str, task_id: int, data: dict):
    """Broadcast task update to subscribed clients."""
    await manager.broadcast_update(event, "task", task_id, data)


async def broadcast_ai_progress(backlog_item_id: int, workflow_id: str, current_task: dict, progress: dict):
    """Broadcast AI progress update to subscribed clients."""
    data = {
        "workflow_id": workflow_id,
        "current_task": current_task,
        "progress": progress
    }
    await manager.broadcast_update("ai_progress", "backlog_item", backlog_item_id, data)