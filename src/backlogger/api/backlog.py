"""BacklogItem and task endpoints."""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, Query, status
from pydantic import BaseModel, ConfigDict, Field
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_async_db
from ..core.backlog_service import BacklogService
from ..core.task_service import TaskService
from ..models.backlog_item import BacklogItemStatus
from ..models.epic import WorkItemStatus

# Pydantic models for BacklogItems
class BacklogItemCreate(BaseModel):
    name: str = Field(..., max_length=200, description="BacklogItem name")
    description: Optional[str] = Field(None, max_length=2000, description="BacklogItem description")


class BacklogItemUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200, description="BacklogItem name")
    description: Optional[str] = Field(None, max_length=2000, description="BacklogItem description")
    status: Optional[BacklogItemStatus] = Field(None, description="BacklogItem status")
    assignee: Optional[str] = Field(None, max_length=100, description="BacklogItem assignee")


class BacklogItemResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    status: BacklogItemStatus
    assignee: Optional[str]
    feature_id: int
    ai_workflow_id: Optional[str]
    created_at: datetime
    updated_at: datetime


class BacklogItemListResponse(BaseModel):
    backlog_items: List[BacklogItemResponse]


# Pydantic models for Tasks
class TaskCreate(BaseModel):
    name: str = Field(..., max_length=200, description="Task name")
    description: Optional[str] = Field(None, max_length=2000, description="Task description")
    processing_order: Optional[int] = Field(None, description="Processing order for sequential execution")


class TaskUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200, description="Task name")
    description: Optional[str] = Field(None, max_length=2000, description="Task description")
    status: Optional[WorkItemStatus] = Field(None, description="Task status")
    assignee: Optional[str] = Field(None, max_length=100, description="Task assignee")
    processing_order: Optional[int] = Field(None, description="Processing order for sequential execution")


class TaskResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    backlog_item_id: int
    processing_order: int
    created_at: datetime
    updated_at: datetime


class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]


class TaskReorderRequest(BaseModel):
    task_orders: List[dict] = Field(..., description="List of {task_id: int, processing_order: int}")


# Router setup
router = APIRouter(prefix="/api/v1", tags=["backlog"])


# BacklogItem endpoints
@router.get("/features/{feature_id}/backlog-items", response_model=BacklogItemListResponse)
async def list_backlog_items(
    feature_id: int,
    status: Optional[BacklogItemStatus] = Query(None, description="Filter by backlog item status"),
    assignee: Optional[str] = Query(None, description="Filter by assignee"),
    db: AsyncSession = Depends(get_async_db)
):
    """List backlog items within a feature."""
    service = BacklogService(db)

    items = await service.list_backlog_items_by_feature(
        feature_id=feature_id,
        status_filter=status,
        assignee_filter=assignee
    )

    return BacklogItemListResponse(
        backlog_items=[BacklogItemResponse.model_validate(item) for item in items]
    )


@router.post("/features/{feature_id}/backlog-items", response_model=BacklogItemResponse, status_code=status.HTTP_201_CREATED)
async def create_backlog_item(
    feature_id: int,
    item_data: BacklogItemCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new backlog item."""
    service = BacklogService(db)

    item = await service.create_backlog_item(
        feature_id=feature_id,
        name=item_data.name,
        description=item_data.description
    )

    return BacklogItemResponse.model_validate(item)


@router.put("/backlog-items/{item_id}", response_model=BacklogItemResponse)
async def update_backlog_item(
    item_id: int,
    item_data: BacklogItemUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """Update backlog item information."""
    service = BacklogService(db)

    item = await service.update_backlog_item(
        item_id=item_id,
        name=item_data.name,
        description=item_data.description,
        status=item_data.status,
        assignee=item_data.assignee
    )

    return BacklogItemResponse.model_validate(item)


@router.delete("/backlog-items/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_backlog_item(
    item_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Delete backlog item and all child entities."""
    service = BacklogService(db)
    await service.delete_backlog_item(item_id)
    return None


# Task endpoints
@router.get("/backlog-items/{item_id}/tasks", response_model=TaskListResponse)
async def list_tasks(
    item_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by task status"),
    assignee: Optional[str] = Query(None, description="Filter by assignee"),
    db: AsyncSession = Depends(get_async_db)
):
    """List tasks within a backlog item, ordered by processing order."""
    service = TaskService(db)

    tasks = await service.list_tasks_by_backlog_item(
        backlog_item_id=item_id,
        status_filter=status,
        assignee_filter=assignee
    )

    return TaskListResponse(
        tasks=[TaskResponse.model_validate(task) for task in tasks]
    )


@router.post("/backlog-items/{item_id}/tasks", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    item_id: int,
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new task."""
    service = TaskService(db)

    task = await service.create_task(
        backlog_item_id=item_id,
        name=task_data.name,
        description=task_data.description,
        processing_order=task_data.processing_order
    )

    return TaskResponse.model_validate(task)


@router.put("/tasks/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_data: TaskUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """Update task information."""
    service = TaskService(db)

    task = await service.update_task(
        task_id=task_id,
        name=task_data.name,
        description=task_data.description,
        status=task_data.status,
        assignee=task_data.assignee,
        processing_order=task_data.processing_order
    )

    return TaskResponse.model_validate(task)


@router.delete("/tasks/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Delete task."""
    service = TaskService(db)
    await service.delete_task(task_id)
    return None


@router.put("/backlog-items/{item_id}/tasks/reorder", response_model=TaskListResponse)
async def reorder_tasks(
    item_id: int,
    reorder_data: TaskReorderRequest,
    db: AsyncSession = Depends(get_async_db)
):
    """Reorder tasks within a backlog item."""
    service = TaskService(db)

    tasks = await service.reorder_tasks(
        backlog_item_id=item_id,
        task_orders=reorder_data.task_orders
    )

    return TaskListResponse(
        tasks=[TaskResponse.model_validate(task) for task in tasks]
    )