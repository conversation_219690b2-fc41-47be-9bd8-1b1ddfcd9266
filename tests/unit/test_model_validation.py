"""Unit tests for model validation."""

import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import from the src package
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from backlogger.core.database import Base
from backlogger.models.project import Project, ProjectStatus
from backlogger.models.epic import Epic, WorkItemStatus
from backlogger.models.feature import Feature
from backlogger.models.backlog_item import BacklogItem, BacklogItemStatus
from backlogger.models.task import Task


class TestProjectModel:
    """Test Project model validation."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_project_creation_valid(self, session):
        """Test valid project creation."""
        project = Project(
            name="Test Project",
            description="A test project",
            status=ProjectStatus.ACTIVE
        )
        session.add(project)
        session.commit()
        
        assert project.id is not None
        assert project.name == "Test Project"
        assert project.description == "A test project"
        assert project.status == ProjectStatus.ACTIVE
        assert isinstance(project.created_at, datetime)
        assert isinstance(project.updated_at, datetime)
    
    def test_project_name_required(self, session):
        """Test that project name is required."""
        with pytest.raises(Exception):  # SQLAlchemy will raise exception for null name
            project = Project(description="No name project")
            session.add(project)
            session.commit()
    
    def test_project_name_unique(self, session):
        """Test that project names must be unique."""
        project1 = Project(name="Unique Name")
        session.add(project1)
        session.commit()
        
        project2 = Project(name="Unique Name")
        session.add(project2)
        
        with pytest.raises(Exception):  # SQLAlchemy will raise IntegrityError
            session.commit()
    
    def test_project_status_enum(self, session):
        """Test project status enum validation."""
        project = Project(name="Status Test", status=ProjectStatus.COMPLETED)
        session.add(project)
        session.commit()
        
        assert project.status == ProjectStatus.COMPLETED
    
    def test_project_description_optional(self, session):
        """Test that description is optional."""
        project = Project(name="No Description Project")
        session.add(project)
        session.commit()
        
        assert project.description is None


class TestEpicModel:
    """Test Epic model validation."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_epic_creation_valid(self, session):
        """Test valid epic creation."""
        # Create parent project first
        project = Project(name="Parent Project")
        session.add(project)
        session.commit()
        
        epic = Epic(
            name="Test Epic",
            description="A test epic",
            status=WorkItemStatus.TODO,
            project_id=project.id
        )
        session.add(epic)
        session.commit()
        
        assert epic.id is not None
        assert epic.name == "Test Epic"
        assert epic.project_id == project.id
        assert epic.status == WorkItemStatus.TODO
    
    def test_epic_project_relationship(self, session):
        """Test epic-project relationship."""
        project = Project(name="Parent Project")
        session.add(project)
        session.commit()
        
        epic = Epic(name="Test Epic", project_id=project.id)
        session.add(epic)
        session.commit()
        
        # Test relationship access
        assert epic.project.name == "Parent Project"
        assert epic in project.epics


class TestFeatureModel:
    """Test Feature model validation."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_feature_creation_valid(self, session):
        """Test valid feature creation."""
        # Create parent structures
        project = Project(name="Parent Project")
        session.add(project)
        session.commit()
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.commit()
        
        feature = Feature(
            name="Test Feature",
            description="A test feature",
            status=WorkItemStatus.TODO,
            epic_id=epic.id
        )
        session.add(feature)
        session.commit()
        
        assert feature.id is not None
        assert feature.name == "Test Feature"
        assert feature.epic_id == epic.id
        assert feature.status == WorkItemStatus.TODO
    
    def test_feature_validation_rules(self, session):
        """Test feature validation rules."""
        project = Project(name="Parent Project")
        session.add(project)
        session.flush()  # Get the project ID without committing
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.flush()  # Get the epic ID without committing
        
        # Test basic feature creation
        feature = Feature(
            name="Test Feature",
            epic_id=epic.id,
            description="Test feature description"
        )
        session.add(feature)
        session.commit()
        
        assert feature.name == "Test Feature"
        assert feature.description == "Test feature description"
        assert feature.epic_id == epic.id


class TestBacklogItemModel:
    """Test BacklogItem model validation."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_backlog_item_with_ai_fields(self, session):
        """Test backlog item with AI workflow fields."""
        # Create parent structures
        project = Project(name="Parent Project")
        session.add(project)
        session.flush()  # Get the project ID
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.flush()  # Get the epic ID
        
        feature = Feature(name="Parent Feature", epic_id=epic.id)
        session.add(feature)
        session.flush()  # Get the feature ID
        
        backlog_item = BacklogItem(
            name="Test Backlog Item",
            description="A test backlog item",
            feature_id=feature.id,
            status=BacklogItemStatus.TODO,
            ai_workflow_id=None
        )
        session.add(backlog_item)
        session.commit()
        
        assert backlog_item.id is not None
        assert backlog_item.ai_workflow_id is None
        assert backlog_item.status == BacklogItemStatus.TODO
    
    def test_ai_workflow_state_transitions(self, session):
        """Test AI workflow state validations."""
        project = Project(name="Parent Project")
        session.add(project)
        session.flush()
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.flush()
        
        feature = Feature(name="Parent Feature", epic_id=epic.id)
        session.add(feature)
        session.flush()
        
        backlog_item = BacklogItem(
            name="AI Test Item",
            feature_id=feature.id,
            ai_workflow_id="test-workflow-123",
            status=BacklogItemStatus.IN_PROGRESS
        )
        session.add(backlog_item)
        session.commit()
        
        assert backlog_item.ai_workflow_id == "test-workflow-123"
        assert backlog_item.status == BacklogItemStatus.IN_PROGRESS
        assert backlog_item.is_ai_processing() is True


class TestTaskModel:
    """Test Task model validation."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_task_with_processing_order(self, session):
        """Test task with processing order field."""
        # Create parent structures
        project = Project(name="Parent Project")
        session.add(project)
        session.flush()
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.flush()
        
        feature = Feature(name="Parent Feature", epic_id=epic.id)
        session.add(feature)
        session.flush()
        
        backlog_item = BacklogItem(name="Parent Item", feature_id=feature.id)
        session.add(backlog_item)
        session.flush()
        
        task = Task(
            name="Test Task",
            description="A test task",
            backlog_item_id=backlog_item.id,
            status=WorkItemStatus.TODO,
            processing_order=1
        )
        session.add(task)
        session.commit()
        
        assert task.id is not None
        assert task.processing_order == 1
        assert task.status == WorkItemStatus.TODO
    
    def test_task_sequential_processing(self, session):
        """Test tasks can be ordered for sequential processing."""
        project = Project(name="Parent Project")
        session.add(project)
        session.flush()
        
        epic = Epic(name="Parent Epic", project_id=project.id)
        session.add(epic)
        session.flush()
        
        feature = Feature(name="Parent Feature", epic_id=epic.id)
        session.add(feature)
        session.flush()
        
        backlog_item = BacklogItem(name="Parent Item", feature_id=feature.id)
        session.add(backlog_item)
        session.flush()
        
        # Create multiple tasks with different orders
        task1 = Task(name="First Task", backlog_item_id=backlog_item.id, processing_order=1)
        task2 = Task(name="Second Task", backlog_item_id=backlog_item.id, processing_order=2)
        task3 = Task(name="Third Task", backlog_item_id=backlog_item.id, processing_order=3)
        
        session.add_all([task1, task2, task3])
        session.commit()
        
        # Verify ordering
        ordered_tasks = session.query(Task).filter_by(backlog_item_id=backlog_item.id).order_by(Task.processing_order).all()
        assert len(ordered_tasks) == 3
        assert ordered_tasks[0].name == "First Task"
        assert ordered_tasks[1].name == "Second Task"
        assert ordered_tasks[2].name == "Third Task"


class TestModelRelationships:
    """Test relationships between models."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    def test_complete_hierarchy(self, session):
        """Test complete project hierarchy relationships."""
        # Create complete hierarchy
        project = Project(name="Complete Project")
        session.add(project)
        session.commit()
        
        epic = Epic(name="Complete Epic", project_id=project.id)
        session.add(epic)
        session.commit()
        
        feature = Feature(name="Complete Feature", epic_id=epic.id)
        session.add(feature)
        session.commit()
        
        backlog_item = BacklogItem(name="Complete Item", feature_id=feature.id)
        session.add(backlog_item)
        session.commit()
        
        task = Task(name="Complete Task", backlog_item_id=backlog_item.id, processing_order=1)
        session.add(task)
        session.commit()
        
        # Test all relationships work
        assert task.backlog_item.name == "Complete Item"
        assert task.backlog_item.feature.name == "Complete Feature"
        assert task.backlog_item.feature.epic.name == "Complete Epic"
        assert task.backlog_item.feature.epic.project.name == "Complete Project"
        
        # Test reverse relationships
        assert task in backlog_item.tasks
        assert backlog_item in feature.backlog_items
        assert feature in epic.features
        assert epic in project.epics
