"""Unit tests for business logic services."""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import from the src package
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from backlogger.core.database import Base
from backlogger.core.project_service import ProjectService
from backlogger.core.epic_service import EpicService
from backlogger.core.feature_service import FeatureService
from backlogger.core.backlog_service import BacklogService
from backlogger.core.task_service import TaskService
from backlogger.models.project import Project, ProjectStatus
from backlogger.models.epic import Epic, WorkItemStatus
from backlogger.models.feature import Feature
from backlogger.models.backlog_item import BacklogItem, BacklogItemStatus
from backlogger.models.task import Task


class TestProjectService:
    """Test ProjectService business logic."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def project_service(self, session):
        """Create ProjectService instance."""
        return ProjectService(session)
    
    def test_create_project_success(self, project_service, session):
        """Test successful project creation."""
        project = project_service.create_project("Test Project", "Test Description")
        
        assert project.name == "Test Project"
        assert project.description == "Test Description"
        assert project.status == ProjectStatus.ACTIVE
        assert project.id is not None
    
    def test_create_project_validation_empty_name(self, project_service):
        """Test project creation with empty name fails."""
        with pytest.raises(ValueError, match="Project name is required"):
            project_service.create_project("")
        
        with pytest.raises(ValueError, match="Project name is required"):
            project_service.create_project("   ")
    
    def test_create_project_validation_name_too_long(self, project_service):
        """Test project creation with name too long fails."""
        long_name = "x" * 201
        with pytest.raises(ValueError, match="Project name cannot exceed 200 characters"):
            project_service.create_project(long_name)
    
    def test_create_project_validation_description_too_long(self, project_service):
        """Test project creation with description too long fails."""
        long_description = "x" * 2001
        with pytest.raises(ValueError, match="Project description cannot exceed 2000 characters"):
            project_service.create_project("Valid Name", long_description)
    
    def test_create_project_duplicate_name(self, project_service):
        """Test project creation with duplicate name fails."""
        project_service.create_project("Duplicate Name")
        
        with pytest.raises(ValueError, match="Project with name 'Duplicate Name' already exists"):
            project_service.create_project("Duplicate Name")
    
    def test_get_project_by_id(self, project_service):
        """Test retrieving project by ID."""
        created = project_service.create_project("Test Project")
        retrieved = project_service.get_project_by_id(created.id)
        
        assert retrieved is not None
        assert retrieved.id == created.id
        assert retrieved.name == "Test Project"
    
    def test_get_project_by_id_not_found(self, project_service):
        """Test retrieving non-existent project returns None."""
        result = project_service.get_project_by_id(99999)
        assert result is None
    
    def test_update_project_status(self, project_service):
        """Test updating project status."""
        project = project_service.create_project("Test Project")
        initial_updated_at = project.updated_at
        
        import time
        time.sleep(0.01)  # Small delay to ensure timestamp difference
        
        updated = project_service.update_project_status(project.id, ProjectStatus.COMPLETED)
        
        assert updated.status == ProjectStatus.COMPLETED
        assert updated.updated_at >= initial_updated_at
    
    def test_list_projects_with_filter(self, project_service):
        """Test listing projects with status filter."""
        project1 = project_service.create_project("Active Project")
        project2 = project_service.create_project("Another Project")
        project_service.update_project_status(project2.id, ProjectStatus.COMPLETED)

        active_projects, total_active = project_service.list_projects(status=ProjectStatus.ACTIVE)
        completed_projects, total_completed = project_service.list_projects(status=ProjectStatus.COMPLETED)
        
        assert len(active_projects) == 1
        assert total_active == 1
        assert active_projects[0].name == "Active Project"
        
        assert len(completed_projects) == 1 
        assert total_completed == 1
        assert completed_projects[0].name == "Another Project"
class TestEpicService:
    """Test EpicService business logic."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def epic_service(self, session):
        """Create EpicService instance."""
        return EpicService(session)
    
    @pytest.fixture
    def project_service(self, session):
        """Create ProjectService instance."""
        return ProjectService(session)
    
    @pytest.fixture
    def test_project(self, project_service):
        """Create a test project."""
        return project_service.create_project("Test Project")
    
    def test_create_epic_success(self, epic_service, test_project):
        """Test successful epic creation."""
        epic = epic_service.create_epic(
            name="Test Epic",
            description="Test Description",
            project_id=test_project.id
        )
        
        assert epic.name == "Test Epic"
        assert epic.description == "Test Description"
        assert epic.project_id == test_project.id
        assert epic.status == WorkItemStatus.TODO
    
    def test_create_epic_invalid_project(self, epic_service):
        """Test epic creation with invalid project ID fails."""
        with pytest.raises(ValueError, match="Project with ID 99999 not found"):
            epic_service.create_epic("Test Epic", project_id=99999)
    
    def test_epic_status_transitions(self, epic_service, test_project):
        """Test epic status transition validation."""
        epic = epic_service.create_epic("Test Epic", project_id=test_project.id)
        
        # Valid transition: TODO -> IN_PROGRESS (requires assignee)
        updated = epic_service.update_epic_status(epic.id, WorkItemStatus.IN_PROGRESS, assignee="test_user")
        assert updated.status == WorkItemStatus.IN_PROGRESS
        assert updated.assignee == "test_user"
        
        # Valid transition: IN_PROGRESS -> DONE (need assignee for DONE status too)
        updated = epic_service.update_epic_status(epic.id, WorkItemStatus.DONE, assignee="test_user")
        assert updated.status == WorkItemStatus.DONE
        
        # Note: DONE state has no valid transitions according to business logic
        # So we cannot transition from DONE to TODO


class TestFeatureService:
    """Test FeatureService business logic."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def feature_service(self, session):
        """Create FeatureService instance."""
        return FeatureService(session)
    
    @pytest.fixture
    def test_epic(self, session):
        """Create a test epic."""
        project = Project(name="Test Project")
        session.add(project)
        session.commit()
        
        epic = Epic(name="Test Epic", project_id=project.id)
        session.add(epic)
        session.commit()
        return epic
    
    def test_create_feature_with_validation(self, feature_service, test_epic):
        """Test feature creation with validation rules."""
        feature = feature_service.create_feature(
            name="Test Feature",
            description="Test Description",
            epic_id=test_epic.id
        )
        
        assert feature.name == "Test Feature"
        assert feature.epic_id == test_epic.id
        assert feature.description == "Test Description"
        assert feature.status == WorkItemStatus.TODO
    
    def test_feature_creation_validation(self, feature_service, test_epic):
        """Test feature creation validation rules."""
        # Valid feature creation
        feature = feature_service.create_feature(
            name="Valid Feature",
            epic_id=test_epic.id
        )
        
        # Test expected values
        assert feature.name == "Valid Feature"
        assert feature.epic_id == test_epic.id
        assert feature.status == WorkItemStatus.TODO
    
    def test_feature_story_points_validation(self, feature_service, test_epic):
        """Test feature description validation."""
        # Valid description
        feature = feature_service.create_feature(
            name="Feature with description",
            epic_id=test_epic.id,
            description="This is a valid description"
        )
        assert feature.description == "This is a valid description"


class TestBacklogService:
    """Test BacklogService business logic."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def backlog_service(self, session):
        """Create BacklogService instance."""
        return BacklogService(session)
    
    @pytest.fixture
    def test_feature(self, session):
        """Create a test feature."""
        project = Project(name="Test Project")
        session.add(project)
        session.flush()  # Generate project.id
        
        epic = Epic(name="Test Epic", project_id=project.id)
        session.add(epic)
        session.flush()  # Generate epic.id
        
        feature = Feature(name="Test Feature", epic_id=epic.id)
        session.add(feature)
        session.commit()
        return feature
    
    def test_trigger_ai_workflow(self, backlog_service, test_feature):
        """Test AI workflow trigger functionality."""
        backlog_item = BacklogItem(
            name="Test Item",
            feature_id=test_feature.id,
            status=BacklogItemStatus.TODO  # Use status instead of ai_ready
        )
        backlog_service.db.add(backlog_item)
        backlog_service.db.commit()
        
        # Create a task so workflow can be started
        from backlogger.models.task import Task
        task = Task(
            name="Test Task",
            backlog_item_id=backlog_item.id,
            processing_order=1
        )
        backlog_service.db.add(task)
        backlog_service.db.commit()
        
        # Trigger AI workflow using ready_for_ai
        result = backlog_service.ready_for_ai(backlog_item.id)
        
        # Check that workflow was triggered
        assert result.status == BacklogItemStatus.READY_FOR_AI
    
    def test_ai_workflow_already_processing(self, backlog_service, test_feature):
        """Test AI workflow trigger when already processing."""
        backlog_item = BacklogItem(
            name="Test Item",
            feature_id=test_feature.id,
            status=BacklogItemStatus.IN_PROGRESS,  # Use status instead of ai_processing
            ai_workflow_id="existing_workflow"  # Indicate it's already processing
        )
        backlog_service.db.add(backlog_item)
        backlog_service.db.commit()
        
        # Create a task 
        from backlogger.models.task import Task
        task = Task(
            name="Test Task",
            backlog_item_id=backlog_item.id,
            processing_order=1
        )
        backlog_service.db.add(task)
        backlog_service.db.commit()
        
        # Try to mark as ready again - should fail because it's already in progress
        try:
            result = backlog_service.ready_for_ai(backlog_item.id)
            assert False, "Should have raised an error"
        except ValueError as e:
            assert "Can only move TODO items to ready_for_ai" in str(e)
    
    @patch('backlogger.ai_workflows.workflow_manager.WorkflowManager.start_ai_workflow')
    def test_ai_workflow_integration(self, mock_start_workflow, backlog_service, test_feature):
        """Test AI workflow integration with workflow manager."""
        mock_start_workflow.return_value = "workflow_123"  # Return workflow ID
        
        backlog_item = BacklogItem(name="Test Item", feature_id=test_feature.id)
        backlog_service.db.add(backlog_item)
        backlog_service.db.commit()
        
        # Create a task 
        from backlogger.models.task import Task
        task = Task(
            name="Test Task",
            backlog_item_id=backlog_item.id,
            processing_order=1
        )
        backlog_service.db.add(task)
        backlog_service.db.commit()
        
        result = backlog_service.ready_for_ai(backlog_item.id)
        
        # Verify result
        assert result.status == BacklogItemStatus.READY_FOR_AI


class TestTaskService:
    """Test TaskService business logic."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def task_service(self, session):
        """Create TaskService instance."""
        return TaskService(session)
    
    @pytest.fixture
    def test_backlog_item(self, session):
        """Create a test backlog item."""
        project = Project(name="Test Project")
        session.add(project)
        session.flush()  # Generate project.id
        
        epic = Epic(name="Test Epic", project_id=project.id)
        session.add(epic)
        session.flush()  # Generate epic.id
        
        feature = Feature(name="Test Feature", epic_id=epic.id)
        session.add(feature)
        session.flush()  # Generate feature.id
        
        backlog_item = BacklogItem(name="Test Item", feature_id=feature.id)
        session.add(backlog_item)
        session.commit()
        return backlog_item
    
    def test_create_task_with_processing_order(self, task_service, test_backlog_item):
        """Test task creation with sequential processing order."""
        # Create first task
        task1 = task_service.create_task(
            name="First Task",
            description="First task description",
            backlog_item_id=test_backlog_item.id,
            processing_order=1
        )
        
        # Create second task  
        task2 = task_service.create_task(
            name="Second Task",
            description="Second task description",
            backlog_item_id=test_backlog_item.id,
            processing_order=2
        )
        
        assert task1.processing_order == 1
        assert task2.processing_order == 2
        assert task1.status == WorkItemStatus.TODO
        assert task2.status == WorkItemStatus.TODO

    def test_get_next_task_for_processing(self, task_service, test_backlog_item):
        """Test getting tasks for AI processing."""
        # Create multiple tasks
        task1 = task_service.create_task("Task 1", backlog_item_id=test_backlog_item.id, processing_order=1)
        task2 = task_service.create_task("Task 2", backlog_item_id=test_backlog_item.id, processing_order=2)
        task3 = task_service.create_task("Task 3", backlog_item_id=test_backlog_item.id, processing_order=3)
        
        # Get tasks for processing (using existing method)
        tasks = task_service.get_tasks_for_ai_processing(test_backlog_item.id)
        
        assert len(tasks) == 3
        assert tasks[0].processing_order == 1
        assert tasks[1].processing_order == 2
        assert tasks[2].processing_order == 3

    def test_sequential_task_processing(self, task_service, test_backlog_item):
        """Test task status transitions."""
        # Create tasks
        task1 = task_service.create_task("Task 1", backlog_item_id=test_backlog_item.id, processing_order=1)
        task2 = task_service.create_task("Task 2", backlog_item_id=test_backlog_item.id, processing_order=2)
        
        # Start processing first task using status transition
        updated_task1 = task_service.transition_status(task1.id, WorkItemStatus.IN_PROGRESS, assignee="test_user")
        assert updated_task1.status == WorkItemStatus.IN_PROGRESS
        assert updated_task1.assignee == "test_user"
        
        # Complete first task
        completed_task1 = task_service.transition_status(task1.id, WorkItemStatus.DONE, assignee="test_user")
        assert completed_task1.status == WorkItemStatus.DONE
        
        # Verify second task still in TODO
        task2_current = task_service.get_task_by_id(task2.id)
        assert task2_current.status == WorkItemStatus.TODO

    def test_task_processing_with_delays(self, task_service, test_backlog_item):
        """Test task assignment to AI."""
        task = task_service.create_task("Delayed Task", backlog_item_id=test_backlog_item.id, processing_order=1)
        
        # Assign to AI
        updated_task = task_service.assign_to_ai(task.id)
        assert updated_task.assignee == "ai"  # Service uses lowercase "ai"
        assert updated_task.status == WorkItemStatus.IN_PROGRESS
        
        # Complete AI task
        completed_task = task_service.complete_ai_task(task.id)
        assert completed_task.status == WorkItemStatus.DONE
class TestServiceIntegration:
    """Test integration between services."""
    
    @pytest.fixture
    def session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def services(self, session):
        """Create all service instances."""
        return {
            'project': ProjectService(session),
            'epic': EpicService(session),
            'feature': FeatureService(session),
            'backlog': BacklogService(session),
            'task': TaskService(session)
        }
    
    def test_complete_workflow_integration(self, services):
        """Test complete workflow from project to task completion."""
        # Create project
        project = services['project'].create_project("Integration Test Project")
        
        # Create epic
        epic = services['epic'].create_epic("Integration Epic", project_id=project.id)
        
        # Create feature
        feature = services['feature'].create_feature("Integration Feature", epic_id=epic.id)
        
        # Create backlog item
        backlog_item = services['backlog'].create_backlog_item(
            "Integration Item", 
            feature_id=feature.id
        )
        
        # Create tasks
        task1 = services['task'].create_task("Task 1", backlog_item_id=backlog_item.id, processing_order=1)
        task2 = services['task'].create_task("Task 2", backlog_item_id=backlog_item.id, processing_order=2)
        
        # Verify complete hierarchy
        assert task1.backlog_item.feature.epic.project.name == "Integration Test Project"
        assert task1.processing_order == 1
        assert task2.processing_order == 2
        
        # Test status propagation (with assignee)
        services['epic'].update_epic_status(epic.id, WorkItemStatus.IN_PROGRESS, assignee="test_user")
        updated_epic = services['epic'].get_epic_by_id(epic.id)
        assert updated_epic.status == WorkItemStatus.IN_PROGRESS
        assert updated_epic.assignee == "test_user"
