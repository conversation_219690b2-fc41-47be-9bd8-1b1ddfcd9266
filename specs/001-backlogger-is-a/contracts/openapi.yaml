openapi: 3.0.3
info:
  title: Backlogger API
  description: Smart AI-integrated backlog management system
  version: 1.0.0
  contact:
    name: Backlogger Team
servers:
  - url: http://localhost:8000/api/v1
    description: Development server

components:
  schemas:
    Project:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        status:
          type: string
          enum: [active, paused, completed, archived]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required: [id, name, status, created_at, updated_at]

    Epic:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        status:
          type: string
          enum: [todo, in_progress, done]
        assignee:
          type: string
        project_id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required: [id, name, status, project_id, created_at, updated_at]

    Feature:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        status:
          type: string
          enum: [todo, in_progress, done]
        assignee:
          type: string
        epic_id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required: [id, name, status, epic_id, created_at, updated_at]

    BacklogItem:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        status:
          type: string
          enum: [todo, ready_for_ai, in_progress, acceptance, done]
        assignee:
          type: string
        feature_id:
          type: integer
        ai_workflow_id:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required: [id, name, status, feature_id, created_at, updated_at]

    Task:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        status:
          type: string
          enum: [todo, in_progress, done]
        assignee:
          type: string
        backlog_item_id:
          type: integer
        processing_order:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required: [id, name, status, backlog_item_id, processing_order, created_at, updated_at]

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object
        timestamp:
          type: string
          format: date-time
      required: [error, message, timestamp]

paths:
  /projects:
    get:
      summary: List all projects
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, paused, completed, archived]
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of projects
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer

    post:
      summary: Create a new project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name]
              properties:
                name:
                  type: string
                  maxLength: 200
                description:
                  type: string
                  maxLength: 2000
      responses:
        '201':
          description: Project created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /projects/{project_id}:
    get:
      summary: Get project details
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: integer
        - name: include
          in: query
          schema:
            type: string
            enum: [epics, epics.features, epics.features.backlog_items]
      responses:
        '200':
          description: Project details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      summary: Update project
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 200
                description:
                  type: string
                  maxLength: 2000
                status:
                  type: string
                  enum: [active, paused, completed, archived]
      responses:
        '200':
          description: Project updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '404':
          description: Project not found
        '400':
          description: Validation error

    delete:
      summary: Delete project
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Project deleted successfully
        '404':
          description: Project not found
        '409':
          description: Cannot delete project with active epics

  /projects/{project_id}/epics:
    get:
      summary: List epics in project
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: integer
        - name: status
          in: query
          schema:
            type: string
            enum: [todo, in_progress, done]
        - name: assignee
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of epics
          content:
            application/json:
              schema:
                type: object
                properties:
                  epics:
                    type: array
                    items:
                      $ref: '#/components/schemas/Epic'

    post:
      summary: Create new epic
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name]
              properties:
                name:
                  type: string
                  maxLength: 200
                description:
                  type: string
                  maxLength: 2000
      responses:
        '201':
          description: Epic created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Epic'

  /backlog-items/{item_id}/ready-for-ai:
    post:
      summary: Mark backlog item as ready for AI processing
      parameters:
        - name: item_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: AI workflow triggered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  workflow_id:
                    type: string
                  status:
                    type: string
                    enum: [ready_for_ai]
                  estimated_completion:
                    type: string
                    format: date-time
        '400':
          description: Item not eligible for AI processing
        '409':
          description: Item already in AI workflow

  /backlog-items/{item_id}/ai-status:
    get:
      summary: Get AI processing status
      parameters:
        - name: item_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: AI processing status
          content:
            application/json:
              schema:
                type: object
                properties:
                  workflow_id:
                    type: string
                  status:
                    type: string
                  current_task:
                    type: object
                    properties:
                      id:
                        type: integer
                      name:
                        type: string
                      status:
                        type: string
                  progress:
                    type: object
                    properties:
                      completed_tasks:
                        type: integer
                      total_tasks:
                        type: integer
                      percentage:
                        type: number
                      estimated_remaining:
                        type: string
        '404':
          description: No AI workflow found for this item
