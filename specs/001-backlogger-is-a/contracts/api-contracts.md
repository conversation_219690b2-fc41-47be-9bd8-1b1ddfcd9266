# API Contracts: Backlogger Management System

**Date**: September 7, 2025  
**Base URL**: `/api/v1`  
**Authentication**: Session-based (to be implemented)

## Project Management

### GET /projects
**Purpose**: List all projects with optional filtering

```yaml
parameters:
  - name: status
    in: query
    type: string
    enum: [active, paused, completed, archived]
  - name: limit
    in: query
    type: integer
    default: 50
  - name: offset
    in: query
    type: integer
    default: 0

responses:
  200:
    description: List of projects
    content:
      application/json:
        schema:
          type: object
          properties:
            projects:
              type: array
              items:
                $ref: '#/components/schemas/Project'
            total:
              type: integer
            limit:
              type: integer
            offset:
              type: integer
```

### POST /projects
**Purpose**: Create a new project

```yaml
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        required: [name]
        properties:
          name:
            type: string
            maxLength: 200
          description:
            type: string
            maxLength: 2000

responses:
  201:
    description: Project created successfully
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/Project'
  400:
    description: Validation error
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/Error'
```

### GET /projects/{project_id}
**Purpose**: Get project details with relationships

```yaml
parameters:
  - name: project_id
    in: path
    required: true
    type: integer
  - name: include
    in: query
    type: string
    enum: [epics, epics.features, epics.features.backlog_items]

responses:
  200:
    description: Project details
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/ProjectDetail'
  404:
    description: Project not found
```

### PUT /projects/{project_id}
**Purpose**: Update project information

```yaml
parameters:
  - name: project_id
    in: path
    required: true
    type: integer

requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          name:
            type: string
            maxLength: 200
          description:
            type: string
            maxLength: 2000
          status:
            type: string
            enum: [active, paused, completed, archived]

responses:
  200:
    description: Project updated successfully
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/Project'
  404:
    description: Project not found
  400:
    description: Validation error
```

### DELETE /projects/{project_id}
**Purpose**: Delete project and all child entities

```yaml
parameters:
  - name: project_id
    in: path
    required: true
    type: integer

responses:
  204:
    description: Project deleted successfully
  404:
    description: Project not found
  409:
    description: Cannot delete project with active epics
```

## Epic Management

### GET /projects/{project_id}/epics
**Purpose**: List epics within a project

```yaml
parameters:
  - name: project_id
    in: path
    required: true
    type: integer
  - name: status
    in: query
    type: string
    enum: [todo, in_progress, done]
  - name: assignee
    in: query
    type: string

responses:
  200:
    description: List of epics
    content:
      application/json:
        schema:
          type: object
          properties:
            epics:
              type: array
              items:
                $ref: '#/components/schemas/Epic'
```

### POST /projects/{project_id}/epics
**Purpose**: Create a new epic

```yaml
parameters:
  - name: project_id
    in: path
    required: true
    type: integer

requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        required: [name]
        properties:
          name:
            type: string
            maxLength: 200
          description:
            type: string
            maxLength: 2000

responses:
  201:
    description: Epic created successfully
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/Epic'
```

### PUT /epics/{epic_id}
**Purpose**: Update epic status and assignee

```yaml
parameters:
  - name: epic_id
    in: path
    required: true
    type: integer

requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          name:
            type: string
            maxLength: 200
          description:
            type: string
            maxLength: 2000
          status:
            type: string
            enum: [todo, in_progress, done]
          assignee:
            type: string

responses:
  200:
    description: Epic updated successfully
  400:
    description: Invalid status transition or missing assignee
```

## Backlog Item AI Automation

### POST /backlog-items/{item_id}/ready-for-ai
**Purpose**: Mark backlog item as ready for AI processing

```yaml
parameters:
  - name: item_id
    in: path
    required: true
    type: integer

responses:
  200:
    description: AI workflow triggered successfully
    content:
      application/json:
        schema:
          type: object
          properties:
            workflow_id:
              type: string
            status:
              type: string
              enum: [ready_for_ai]
            estimated_completion:
              type: string
              format: date-time
  400:
    description: Item not eligible for AI processing
  409:
    description: Item already in AI workflow
```

### GET /backlog-items/{item_id}/ai-status
**Purpose**: Get current AI processing status

```yaml
parameters:
  - name: item_id
    in: path
    required: true
    type: integer

responses:
  200:
    description: AI processing status
    content:
      application/json:
        schema:
          type: object
          properties:
            workflow_id:
              type: string
            status:
              type: string
            current_task:
              type: object
              properties:
                id:
                  type: integer
                name:
                  type: string
                status:
                  type: string
            progress:
              type: object
              properties:
                completed_tasks:
                  type: integer
                total_tasks:
                  type: integer
                percentage:
                  type: number
                estimated_remaining:
                  type: string
                  format: duration
  404:
    description: No AI workflow found for this item
```

## Real-time WebSocket

### WebSocket /ws/dashboard
**Purpose**: Real-time updates for dashboard UI

```yaml
connection:
  protocol: WebSocket
  authentication: Session token in query parameter

message_types:
  # Client to Server
  subscribe:
    type: object
    properties:
      action:
        type: string
        enum: [subscribe]
      resource:
        type: string
        enum: [project, epic, feature, backlog_item, task]
      resource_id:
        type: integer

  # Server to Client
  update:
    type: object
    properties:
      event:
        type: string
        enum: [created, updated, deleted, ai_progress]
      resource:
        type: string
      resource_id:
        type: integer
      data:
        type: object
      timestamp:
        type: string
        format: date-time

  ai_progress:
    type: object
    properties:
      event:
        type: string
        enum: [ai_progress]
      backlog_item_id:
        type: integer
      workflow_id:
        type: string
      current_task:
        type: object
      progress:
        type: object
```

## Error Schemas

```yaml
components:
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object
        timestamp:
          type: string
          format: date-time

    ValidationError:
      type: object
      properties:
        error:
          type: string
          enum: [validation_error]
        message:
          type: string
        field_errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
```

## Rate Limiting

- **API Endpoints**: 1000 requests per minute per session
- **WebSocket Messages**: 100 messages per minute per connection
- **AI Workflow Triggers**: 10 per hour per user

---
**Contract Status**: Complete ✅  
**Next Step**: Generate contract tests
