# Data Model: Smart AI-Integrated Backlog Management System

**Date**: September 7, 2025  
**Source**: Feature specification and research analysis

## Entity Overview

The system manages a hierarchical structure of work items with AI automation capabilities:

```
Project (1:N) → Epic (1:N) → Feature (1:N) → BacklogItem (1:N) → Task
```

## Core Entities

### Project
**Purpose**: Top-level container for organizing work within a company initiative

```python
class Project(Base):
    __tablename__ = "projects"
    
    id: int (Primary Key)
    name: str (Required, max 200 chars)
    description: Optional[str] (max 2000 chars)
    status: ProjectStatus (enum: active, paused, completed, archived)
    created_at: datetime (auto-generated)
    updated_at: datetime (auto-updated)
    
    # Relationships
    epics: List[Epic] (cascade delete)
```

**Validation Rules**:
- Name must be unique within organization
- Status transitions: active ↔ paused → completed → archived
- Cannot delete project with active epics

### Epic
**Purpose**: Major body of work representing significant business value

```python
class Epic(Base):
    __tablename__ = "epics"
    
    id: int (Primary Key)
    name: str (Required, max 200 chars)
    description: Optional[str] (max 2000 chars)
    status: WorkItemStatus (enum: todo, in_progress, done)
    assignee: Optional[str] (human or AI identifier)
    project_id: int (Foreign Key → Project.id)
    created_at: datetime (auto-generated)
    updated_at: datetime (auto-updated)
    
    # Relationships
    project: Project
    features: List[Feature] (cascade delete)
```

**Validation Rules**:
- Must have assignee when status is in_progress or done
- Cannot complete epic with incomplete features
- Status transitions: todo → in_progress → done

### Feature
**Purpose**: Specific functionality or capability within an epic

```python
class Feature(Base):
    __tablename__ = "features"
    
    id: int (Primary Key)
    name: str (Required, max 200 chars)
    description: Optional[str] (max 2000 chars)
    status: WorkItemStatus (enum: todo, in_progress, done)
    assignee: Optional[str] (human or AI identifier)
    epic_id: int (Foreign Key → Epic.id)
    created_at: datetime (auto-generated)
    updated_at: datetime (auto-updated)
    
    # Relationships
    epic: Epic
    backlog_items: List[BacklogItem] (cascade delete)
```

**Validation Rules**:
- Must have assignee when status is in_progress or done
- Cannot complete feature with incomplete backlog items
- Status transitions: todo → in_progress → done

### BacklogItem
**Purpose**: Detailed work item with AI automation capabilities

```python
class BacklogItem(Base):
    __tablename__ = "backlog_items"
    
    id: int (Primary Key)
    name: str (Required, max 200 chars)
    description: Optional[str] (max 2000 chars)
    status: BacklogItemStatus (enum: todo, ready_for_ai, in_progress, acceptance, done)
    assignee: Optional[str] (human or AI identifier)
    feature_id: int (Foreign Key → Feature.id)
    ai_workflow_id: Optional[str] (Dagster job ID when AI processing)
    created_at: datetime (auto-generated)
    updated_at: datetime (auto-updated)
    
    # Relationships
    feature: Feature
    tasks: List[Task] (cascade delete)
```

**Validation Rules**:
- Assignee must be empty when status is todo or ready_for_ai
- Must have assignee when status is in_progress, acceptance, or done
- Status transitions: todo → ready_for_ai → in_progress → acceptance → done
- When moved to ready_for_ai, triggers AI workflow
- Cannot complete backlog item with incomplete tasks

### Task
**Purpose**: Granular work unit processed by AI or humans

```python
class Task(Base):
    __tablename__ = "tasks"
    
    id: int (Primary Key)
    name: str (Required, max 200 chars)
    description: Optional[str] (max 2000 chars)
    status: WorkItemStatus (enum: todo, in_progress, done)
    assignee: Optional[str] (human or AI identifier)
    backlog_item_id: int (Foreign Key → BacklogItem.id)
    processing_order: int (for sequential AI processing)
    created_at: datetime (auto-generated)
    updated_at: datetime (auto-updated)
    
    # Relationships
    backlog_item: BacklogItem
```

**Validation Rules**:
- Must have assignee when status is in_progress or done
- Processing order must be unique within backlog item
- Status transitions: todo → in_progress → done

## Enums and Constants

### Status Enumerations

```python
class WorkItemStatus(str, Enum):
    TODO = "todo"
    IN_PROGRESS = "in_progress" 
    DONE = "done"

class BacklogItemStatus(str, Enum):
    TODO = "todo"
    READY_FOR_AI = "ready_for_ai"
    IN_PROGRESS = "in_progress"
    ACCEPTANCE = "acceptance"
    DONE = "done"

class ProjectStatus(str, Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"
```

### System Constants

```python
# AI Processing
AI_ASSIGNEE = "ai"
TASK_PROCESSING_DELAY = 60  # seconds between tasks
MAX_PARALLEL_WORKFLOWS = 5

# Validation
MAX_NAME_LENGTH = 200
MAX_DESCRIPTION_LENGTH = 2000
MAX_TASKS_PER_BACKLOG_ITEM = 50
```

## Database Relationships

### Foreign Key Constraints
- All relationships use CASCADE DELETE for data consistency
- Parent entities cannot be deleted with active children
- Orphaned entities are automatically cleaned up

### Indexes for Performance
```sql
-- Frequently queried fields
CREATE INDEX idx_epics_project_status ON epics(project_id, status);
CREATE INDEX idx_features_epic_status ON features(epic_id, status);
CREATE INDEX idx_backlog_items_feature_status ON backlog_items(feature_id, status);
CREATE INDEX idx_tasks_backlog_item_order ON tasks(backlog_item_id, processing_order);

-- AI workflow queries
CREATE INDEX idx_backlog_items_ai_status ON backlog_items(status) WHERE status = 'ready_for_ai';
CREATE INDEX idx_backlog_items_workflow ON backlog_items(ai_workflow_id) WHERE ai_workflow_id IS NOT NULL;
```

## State Transitions and Business Rules

### AI Workflow Trigger
**Event**: BacklogItem.status changes to `ready_for_ai`
**Process**:
1. Validate backlog item has at least one task
2. Create Dagster workflow job
3. Update ai_workflow_id field
4. Trigger sequential task processing

### AI Task Processing
**Process**:
1. Query tasks ordered by processing_order
2. For each task:
   - Update assignee to "ai"
   - Update status to "in_progress"
   - Wait 60 seconds
   - Update status to "done"
3. After all tasks complete:
   - Update backlog item status to "acceptance"
   - Clear ai_workflow_id

### Error Handling
- If AI workflow fails, revert backlog item to "todo" status
- Log all state transitions for audit trail
- Implement rollback mechanisms for partial failures

## Data Migration Strategy

### Version 1.0.0 Schema
- Initial schema creation with all entities
- Seed data for status enums
- Default indexes for performance

### Future Considerations
- Audit trail tables for compliance
- User management and permissions
- Project templates and automation rules
- Advanced AI workflow configurations

---
**Design Status**: Complete ✅  
**Next Step**: Generate API contracts and tests
