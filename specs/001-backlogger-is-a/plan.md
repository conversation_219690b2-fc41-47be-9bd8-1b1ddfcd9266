# Implementation Plan: Smart AI-Integrated Backlog Management System

**Branch**: `001-backlogger-is-a` | **Date**: September 7, 2025 | **Spec**: [spec.md](./spec.md)
**Input**: Feature specification from `/home/<USER>/dev/backlogger2/specs/001-backlogger-is-a/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file (e.g., `CLAUDE.md` for Claude Code, `.github/copilot-instructions.md` for GitHub Copilot, or `GEMINI.md` for Gemini CLI).
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phases 2-4 are executed by other commands:
- Phase 2: /tasks command creates tasks.md
- Phase 3-4: Implementation execution (manual or via tools)

## Summary
Smart AI-integrated backlog management system for multi-project companies featuring hierarchical work organization (Projects → Epics → Features → Backlog Items → Tasks) with automated AI processing workflows. Primary requirement: When backlog items are marked "ready for AI", the system automatically assigns them to AI agents who process related tasks sequentially with real-time progress visibility. Technical approach: FastAPI backend with SQLite/SQLAlchemy, Dagster for task orchestration, and Jinja2-templated dashboard UI for real-time monitoring.

## Technical Context
**Language/Version**: Python 3.11+  
**Primary Dependencies**: FastAPI (web framework), SQLAlchemy (ORM), Dagster (task management), Jinja2 (templating)  
**Storage**: SQLite database with SQLAlchemy ORM  
**Testing**: pytest with integration and contract testing  
**Target Platform**: Linux server (web application)
**Project Type**: web - determines source structure (backend + frontend)  
**Performance Goals**: Real-time UI updates, 1000+ concurrent users  
**Constraints**: <200ms API response time, sequential AI task processing (not parallel)  
**Scale/Scope**: Multi-project companies, hierarchical work management, AI automation workflows
**Package Manager**: uv (modern Python package manager)
**Project Initialization**: uv init for project setup, uv add for dependencies, uv run for execution
**UI Framework**: Jinja2 templating under /dashboard route
**Documentation Source**: Context7 for library-specific implementation guidance

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 1 (single web application project)
- Using framework directly? YES (FastAPI direct usage, no wrappers)
- Single data model? YES (SQLAlchemy models serve both API and DB)
- Avoiding patterns? YES (direct FastAPI usage, avoiding Repository/UoW)

**Architecture**:
- EVERY feature as library? YES (core, models, api, dashboard, ai_workflows as separate libraries)
- Libraries listed: backlogger-core (entities), backlogger-api (endpoints), backlogger-dashboard (UI), backlogger-ai (workflow automation), backlogger-cli (management)
- CLI per library: backlogger-cli with --help/--version/--format support
- Library docs: llms.txt format planned? YES

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? YES (pytest-driven TDD)
- Git commits show tests before implementation? YES (enforced workflow)
- Order: Contract→Integration→E2E→Unit strictly followed? YES
- Real dependencies used? YES (actual SQLite DB, not mocks)
- Integration tests for: new libraries, contract changes, shared schemas? YES
- FORBIDDEN: Implementation before test, skipping RED phase

**Observability**:
- Structured logging included? YES (Python logging with JSON format)
- Frontend logs → backend? YES (dashboard sends logs to API)
- Error context sufficient? YES (detailed error responses)

**Versioning**:
- Version number assigned? YES (1.0.0 - MAJOR.MINOR.BUILD)
- BUILD increments on every change? YES
- Breaking changes handled? YES (parallel tests, migration plan)

## Project Structure

### Documentation (this feature)
```
specs/001-backlogger-is-a/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Web application structure (FastAPI + Jinja2 dashboard)
src/
├── backlogger/
│   ├── __init__.py
│   ├── models/          # SQLAlchemy models (entities)
│   ├── api/             # FastAPI endpoints
│   ├── dashboard/       # Jinja2 templates and static files
│   ├── ai_workflows/    # Dagster tasks and AI automation
│   ├── core/            # Business logic libraries
│   └── cli/             # Command-line interface
├── tests/
│   ├── contract/        # API contract tests
│   ├── integration/     # Cross-component tests
│   └── unit/            # Library unit tests
├── pyproject.toml       # uv project configuration
├── .github/
│   └── copilot-instructions.md  # Agent context file
└── README.md
```

**Structure Decision**: Web application (Option 2 adapted for Python/FastAPI) - Single project with integrated backend and frontend via Jinja2 templating

## Phase 0: Outline & Research
1. **Extract unknowns from Technical Context** above:
   - For each NEEDS CLARIFICATION → research task
   - For each dependency → best practices task
   - For each integration → patterns task

2. **Generate and dispatch research agents**:
   ```
   For each unknown in Technical Context:
     Task: "Research {unknown} for {feature context}"
   For each technology choice:
     Task: "Find best practices for {tech} in {domain}"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: [what was chosen]
   - Rationale: [why chosen]
   - Alternatives considered: [what else evaluated]

**Output**: research.md with all NEEDS CLARIFICATION resolved

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Entity name, fields, relationships
   - Validation rules from requirements
   - State transitions if applicable

2. **Generate API contracts** from functional requirements:
   - For each user action → endpoint
   - Use standard REST/GraphQL patterns
   - Output OpenAPI/GraphQL schema to `/contracts/`

3. **Generate contract tests** from contracts:
   - One test file per endpoint
   - Assert request/response schemas
   - Tests must fail (no implementation yet)

4. **Extract test scenarios** from user stories:
   - Each story → integration test scenario
   - Quickstart test = story validation steps

5. **Update agent file incrementally** (O(1) operation):
   - Run `/scripts/update-agent-context.sh [claude|gemini|copilot]` for your AI assistant
   - If exists: Add only NEW tech from current plan
   - Preserve manual additions between markers
   - Update recent changes (keep last 3)
   - Keep under 150 lines for token efficiency
   - Output to repository root

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs (data-model.md, contracts/, quickstart.md)
- Each SQLAlchemy model → model creation + validation test task [P]
- Each API endpoint → contract test task [P]
- Each user story → integration test task
- AI workflow → Dagster pipeline test task
- WebSocket functionality → real-time update test task
- Implementation tasks to make tests pass (following TDD)

**Specific Task Categories**:
1. **Project Setup** (Tasks 1-3): uv init, dependencies, basic structure
2. **Database Models** (Tasks 4-8): SQLAlchemy entities, relationships, validation [P]
3. **Contract Tests** (Tasks 9-15): API endpoint tests, error handling [P]
4. **Core Libraries** (Tasks 16-20): Business logic, CLI interfaces [P]
5. **AI Workflow** (Tasks 21-25): Dagster pipelines, sequential processing
6. **Dashboard UI** (Tasks 26-30): Jinja2 templates, WebSocket integration
7. **Integration Tests** (Tasks 31-35): End-to-end scenarios from quickstart.md

**Ordering Strategy**:
- TDD order: Tests before implementation (RED-GREEN-REFACTOR)
- Dependency order: Models → Core Logic → API → AI Workflows → Dashboard
- Mark [P] for parallel execution (independent files/components)
- Sequential dependencies clearly marked

**Estimated Output**: 35 numbered, ordered tasks in tasks.md with clear TDD progression

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| [e.g., 4th project] | [current need] | [why 3 projects insufficient] |
| [e.g., Repository pattern] | [specific problem] | [why direct DB access insufficient] |


## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [x] Phase 0: Research complete (/plan command)
- [x] Phase 1: Design complete (/plan command)
- [x] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [x] Post-Design Constitution Check: PASS
- [x] All NEEDS CLARIFICATION resolved
- [ ] Complexity deviations documented

---
*Based on Constitution v2.1.1 - See `/memory/constitution.md`*