# Quickstart Guide: Backlogger AI-Integrated Management System

**Date**: September 7, 2025  
**Purpose**: Validate core user scenarios through step-by-step testing

## Prerequisites

- Python 3.11+ installed
- uv package manager installed
- Project dependencies installed via `uv install`
- Database initialized and running

## Core User Journey Validation

### Scenario 1: Project Setup and Hierarchy Creation

**Goal**: Verify users can create and organize work hierarchically

```bash
# Start the application
uv run python -m backlogger.main

# Navigate to dashboard
open http://localhost:8000/dashboard
```

**Manual Test Steps**:

1. **Create a new project**
   - Click "New Project" button
   - Enter name: "Customer Portal Redesign"
   - Enter description: "Modernize customer portal with new UI/UX"
   - Verify project appears in project list

2. **Create an epic within the project**
   - Click on the project
   - Click "Add Epic"
   - Enter name: "User Authentication System"
   - Verify epic appears under project

3. **Create a feature within the epic**
   - Click on the epic
   - Click "Add Feature"
   - Enter name: "Social Login Integration"
   - Verify feature appears under epic

4. **Create a backlog item within the feature**
   - Click on the feature
   - Click "Add Backlog Item"
   - Enter name: "Implement Google OAuth"
   - Verify backlog item appears under feature

5. **Create tasks within the backlog item**
   - Click on the backlog item
   - Click "Add Task"
   - Add three tasks:
     - "Set up Google OAuth credentials"
     - "Implement OAuth flow in backend"
     - "Add OAuth buttons to login form"
   - Verify all tasks appear with processing order

**Expected Results**:
- All entities created successfully
- Hierarchical structure visible in UI
- Navigation between levels works correctly

### Scenario 2: AI Workflow Automation

**Goal**: Verify AI automation processes backlog items sequentially

**Prerequisites**: Complete Scenario 1 first

**Manual Test Steps**:

1. **Trigger AI workflow**
   - Navigate to the backlog item created in Scenario 1
   - Verify status is "todo"
   - Click "Ready for AI" button
   - Verify status changes to "ready for AI"
   - Verify assignee field is empty

2. **Monitor AI processing**
   - Wait for AI to pick up the item (should be immediate)
   - Verify status changes to "in progress"
   - Verify assignee changes to "ai"
   - Verify workflow ID is assigned

3. **Watch sequential task processing**
   - Observe real-time updates in the dashboard
   - Verify first task status changes to "in progress"
   - Verify first task assignee becomes "ai"
   - Wait 1 minute
   - Verify first task status changes to "done"
   - Verify second task begins processing
   - Repeat for all tasks

4. **Verify completion**
   - After all tasks are done, verify backlog item status changes to "acceptance"
   - Verify AI workflow completes successfully
   - Verify workflow ID is cleared

**Expected Results**:
- Tasks process sequentially, not in parallel
- 1-minute delay between task transitions
- Real-time UI updates show progress
- Final status is "acceptance"

### Scenario 3: Real-time Dashboard Updates

**Goal**: Verify multiple users see real-time updates

**Prerequisites**: Two browser windows or users

**Manual Test Steps**:

1. **Open multiple dashboard sessions**
   - Open dashboard in two different browser windows
   - Navigate both to the same project

2. **Make changes in one session**
   - In window 1: Create a new epic
   - In window 2: Verify epic appears automatically
   - In window 1: Change epic status to "in progress"
   - In window 2: Verify status update appears immediately

3. **Test AI workflow real-time updates**
   - In window 1: Mark a backlog item as "ready for AI"
   - In window 2: Verify AI processing updates appear live
   - Verify both windows show task progress updates
   - Verify completion notifications appear in both windows

**Expected Results**:
- Updates appear within 1 second across all sessions
- No page refresh required
- All status changes propagate correctly

### Scenario 4: API Validation

**Goal**: Verify API endpoints work correctly

**Manual Test Steps**:

```bash
# Test project creation
curl -X POST http://localhost:8000/api/v1/projects \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Project API", "description": "Testing API functionality"}'

# Expected: 201 status with project JSON response

# Test project listing
curl http://localhost:8000/api/v1/projects

# Expected: 200 status with projects array

# Test AI workflow trigger
curl -X POST http://localhost:8000/api/v1/backlog-items/1/ready-for-ai

# Expected: 200 status with workflow_id

# Test AI status monitoring
curl http://localhost:8000/api/v1/backlog-items/1/ai-status

# Expected: 200 status with progress information
```

**Expected Results**:
- All API calls return expected status codes
- Response schemas match OpenAPI specification
- Error handling works correctly for invalid requests

### Scenario 5: Error Handling and Edge Cases

**Goal**: Verify system handles error conditions gracefully

**Manual Test Steps**:

1. **Test invalid state transitions**
   - Try to mark backlog item as "ready for AI" when it has no tasks
   - Verify appropriate error message
   - Try to complete epic with incomplete features
   - Verify validation prevents invalid state

2. **Test AI workflow interruption**
   - Start AI workflow on backlog item
   - During processing, try to manually change task status
   - Verify system prevents conflicts
   - Try to delete backlog item during AI processing
   - Verify deletion is prevented

3. **Test connection resilience**
   - Start AI workflow
   - Refresh browser page during processing
   - Verify real-time updates resume correctly
   - Temporarily disconnect network
   - Verify reconnection and state synchronization

**Expected Results**:
- Clear error messages for invalid operations
- Data consistency maintained during failures
- UI gracefully handles connection issues
- No data loss during error conditions

## Performance Validation

### Response Time Testing

```bash
# Install testing tools
uv add httpx pytest-benchmark

# Run performance tests
uv run pytest tests/performance/ -v
```

**Expected Metrics**:
- API response time: <200ms p95
- WebSocket message latency: <100ms
- Dashboard page load: <2 seconds
- Real-time update propagation: <1 second

### Load Testing

```bash
# Simple load test
for i in {1..100}; do
  curl -X GET http://localhost:8000/api/v1/projects &
done
wait

# Expected: All requests complete successfully
# No database connection errors
# Response times remain under 200ms
```

## Cleanup

After completing all scenarios:

```bash
# Stop the application
# Clean test data if needed
uv run python -m backlogger.cli reset-database --confirm

# Verify clean state
curl http://localhost:8000/api/v1/projects
# Expected: Empty projects array
```

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Verify SQLite file permissions
   - Check database initialization

2. **Dagster not running**
   - Start Dagster daemon: `uv run dagster dev`
   - Verify workflow definitions loaded

3. **WebSocket connection fails**
   - Check firewall settings
   - Verify port 8000 is available

4. **Real-time updates not working**
   - Check browser console for WebSocket errors
   - Verify event handlers are registered

### Success Criteria

✅ All scenarios complete without errors  
✅ Real-time updates work across multiple sessions  
✅ AI workflows process tasks sequentially  
✅ API responses match OpenAPI specification  
✅ Performance metrics meet requirements  
✅ Error handling prevents data corruption  

---
**Quickstart Status**: Complete ✅  
**Next Step**: Update agent context file
